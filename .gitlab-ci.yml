default:
  tags:
    - curl-jen<PERSON>
# the value you should change {File_Name},{Package_Name},{Octopus_Project_Name}
variables:
  AWS_DEFAULT_REGION: ap-northeast-1
  File_Name: 2crm-AccessCrmDataN
  LAMBDA_NAME: 2crm-AccessCrmDataN

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "trigger"
      when: always
    - if: $CI_COMMIT_BRANCH == "develop"
      when: always
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH =~ /release*/
      when: always
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /master/ || $CI_COMMIT_BRANCH =~ /master/
      when: never

stages:
  - artifactory
  - push-s3
  - deploy

build-artifactory:
  stage: artifactory
  image: bitnami/git:latest
  script:
    - git archive --format=zip --output ./$File_Name.zip HEAD lambda_function.py
  artifacts:
    untracked: true
    paths:
      - $File_Name.zip
    expire_in: 30 mins

push-artifactory-to-s3:
  stage: push-s3
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  script:
    - ls -a
    - aws s3 ls
    - aws s3 cp ./$File_Name.zip s3://moxa-mhq-jenkins/lambda/strategy/stage/$File_Name.${CI_PIPELINE_IID}.zip

stage-deploy:
  stage: deploy
  image:
    name: amazon/aws-cli
    entrypoint: [""]
  environment: Staging
  dependencies:
    - build-artifactory
  before_script:
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set region $AWS_DEFAULT_REGION
    - aws configure set output "json"
  script:
    - echo "1 Checking if Lambda function exists ..."
    - |
      if aws lambda get-function --function-name $LAMBDA_NAME 2>/dev/null; then
        echo "  Lambda function exists, updating ..."
        echo "  1.0 Invoke aws-cli to update lambda function code ..."
        aws lambda update-function-code --function-name $LAMBDA_NAME --zip-file fileb://$File_Name.zip
        echo "  1.1 Waiting Lambda function code update ..."
        aws lambda wait function-updated --function-name $LAMBDA_NAME
        echo "2 Update Lambda function configuration ..."
        echo " 2.0 Update lambda function layer and iam role ..."
        aws lambda update-function-configuration \
          --function-name $LAMBDA_NAME \
          --role "${LambdaExecutionRole}" \
          --handler "lambda_function.lambda_handler" \
          --timeout 30 \
          --memory-size 128 \
          --vpc-config SubnetIds="${PrivateSubnetId}",SecurityGroupIds=${SecurityGroupIds} \
          --environment "Variables={\
            CRM_CLIENT_ID=${CRM_CLIENT_ID},\
            CRM_CLIENT_SECRET=${CRM_CLIENT_SECRET},\
            GET_ENDPOINT=${GET_ENDPOINT},\
            CRM_USERNAME=${CRM_USERNAME},\
            CRM_PASSWORD=${CRM_PASSWORD},\
            CRM_TOKEN_URL=${CRM_TOKEN_URL},\
            REDIS_HOST=${REDIS_HOST},\
            POST_ENDPOINT=${POST_ENDPOINT},\
          }" \
          --runtime python3.12 \
          --layers "${LambdaLayer1}" "${LambdaLayer2}"
        echo "  2.1 Waiting Lambda function configuration update ..."
        aws lambda wait function-updated --function-name $LAMBDA_NAME
        echo "Lambda function updated successfully ..."
      else
        echo "  Lambda function does not exist, creating ..."
        aws lambda create-function \
          --function-name $LAMBDA_NAME \
          --runtime python3.12 \
          --role "${LambdaExecutionRole}" \
          --handler "lambda_function.lambda_handler" \
          --zip-file fileb://$File_Name.zip \
          --timeout 30 \
          --memory-size 128 \
          --vpc-config SubnetIds="${PrivateSubnetId}",SecurityGroupIds=${SecurityGroupIds} \
          --environment "Variables={\
            CRM_CLIENT_ID=${CRM_CLIENT_ID},\
            CRM_CLIENT_SECRET=${CRM_CLIENT_SECRET},\
            GET_ENDPOINT=${GET_ENDPOINT},\
            CRM_USERNAME=${CRM_USERNAME},\
            CRM_PASSWORD=${CRM_PASSWORD},\
            CRM_TOKEN_URL=${CRM_TOKEN_URL},\
            REDIS_HOST=${REDIS_HOST},\
            POST_ENDPOINT=${POST_ENDPOINT},\
          }" \
          --layers "${LambdaLayer1}" "${LambdaLayer2}"
        echo "  Waiting for Lambda function creation ..."
        aws lambda wait function-active --function-name $LAMBDA_NAME
        echo "Lambda function created successfully ..."
      fi

production-deploy:
  stage: deploy
  image:
    name: amazon/aws-cli
    entrypoint: [""]
  environment: Production
  when: manual
  dependencies:
    - build-artifactory
  needs:
    - build-artifactory
    - stage-deploy
  before_script:
    - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
    - aws configure set region $AWS_DEFAULT_REGION
    - aws configure set output "json"
  script:
    - echo "1 Checking if Lambda function exists ..."
    - |
      if aws lambda get-function --function-name $LAMBDA_NAME 2>/dev/null; then
        echo "  Lambda function exists, updating ..."
        echo "  1.0 Invoke aws-cli to update lambda function code ..."
        aws lambda update-function-code --function-name $LAMBDA_NAME --zip-file fileb://$File_Name.zip
        echo "  1.1 Waiting Lambda function code update ..."
        aws lambda wait function-updated --function-name $LAMBDA_NAME
        echo "2 Update Lambda function configuration ..."
        echo " 2.0 Update lambda function layer and iam role ..."
        aws lambda update-function-configuration \
          --function-name $LAMBDA_NAME \
          --role "${LambdaExecutionRole}" \
          --handler "lambda_function.lambda_handler" \
          --timeout 30 \
          --memory-size 128 \
          --vpc-config SubnetIds="${PrivateSubnetId}",SecurityGroupIds=${SecurityGroupIds} \
          --environment "Variables={\
            CRM_CLIENT_ID=${CRM_CLIENT_ID},\
            CRM_CLIENT_SECRET=${CRM_CLIENT_SECRET},\
            GET_ENDPOINT=${GET_ENDPOINT},\
            CRM_USERNAME=${CRM_USERNAME},\
            CRM_PASSWORD=${CRM_PASSWORD},\
            CRM_TOKEN_URL=${CRM_TOKEN_URL},\
            REDIS_HOST=${REDIS_HOST},\
            POST_ENDPOINT=${POST_ENDPOINT},\
          }" \
          --runtime python3.12 \
          --layers "${LambdaLayer1}" "${LambdaLayer2}"
        echo "  2.1 Waiting Lambda function configuration update ..."
        aws lambda wait function-updated --function-name $LAMBDA_NAME
        echo "Lambda function updated successfully ..."
      else
        echo "  Lambda function does not exist, creating ..."
        aws lambda create-function \
          --function-name $LAMBDA_NAME \
          --runtime python3.12 \
          --role "${LambdaExecutionRole}" \
          --handler "lambda_function.lambda_handler" \
          --zip-file fileb://$File_Name.zip \
          --timeout 30 \
          --memory-size 128 \
          --vpc-config SubnetIds="${PrivateSubnetId}",SecurityGroupIds=${SecurityGroupIds} \
          --environment "Variables={\
            CRM_CLIENT_ID=${CRM_CLIENT_ID},\
            CRM_CLIENT_SECRET=${CRM_CLIENT_SECRET},\
            GET_ENDPOINT=${GET_ENDPOINT},\
            CRM_USERNAME=${CRM_USERNAME},\
            CRM_PASSWORD=${CRM_PASSWORD},\
            CRM_TOKEN_URL=${CRM_TOKEN_URL},\
            REDIS_HOST=${REDIS_HOST},\
            POST_ENDPOINT=${POST_ENDPOINT},\
          }" \
          --layers "${LambdaLayer1}" "${LambdaLayer2}"
        echo "  Waiting for Lambda function creation ..."
        aws lambda wait function-active --function-name $LAMBDA_NAME
        echo "Lambda function created successfully ..."
      fi
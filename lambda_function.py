import json
import requests
import os
from http import HTT<PERSON>tatus
from aws_xray_sdk.core import patch_all
from aws_lambda_powertools import Logger, Tracer
from aws_lambda_powertools.event_handler import (
    APIGatewayRestResolver,
    Response,
    content_types,
)
from aws_lambda_powertools.utilities.typing import LambdaContext
from lib.lambda_general import (
    create_response_body,
    extract_info_from_api_gateway_proxy_event,
)
from lib.redis_client import RedisClient
import urllib.parse

tracer = Tracer()
logger = Logger(service="api")
app = APIGatewayRestResolver()

patch_all()


def get_crm_token_url():
    try:
        redis_client = RedisClient(os.environ.get("REDIS_HOST"), logger)
        redis_client.connect()
        crm_token = redis_client.get_value("scrmtocrm_token")
        instance_url = redis_client.get_value("scrmtocrm_instance_url")
        if crm_token and instance_url:
            return crm_token.decode("utf-8"), instance_url.decode("utf-8")
    except Exception as e:
        logger.error(f"Error in Redis: {e}")

    try:
        crm_token, instance_url = login_crm()
    except Exception as e:
        logger.error(f"Error in CRM login: {e}")
        raise

    try:
        redis_client.set_key_value("scrmtocrm_token", crm_token, 3600)
        redis_client.set_key_value("scrmtocrm_instance_url", instance_url, 3600)
    except Exception as e:
        logger.error(f"Error in Redis: {e}")

    return crm_token, instance_url


def login_crm():
    crm_token_url = os.environ.get("CRM_TOKEN_URL")
    payload = {
        "grant_type": "password",
        "client_id": os.environ.get("CRM_CLIENT_ID"),
        "client_secret": os.environ.get("CRM_CLIENT_SECRET"),
        "username": os.environ.get("CRM_USERNAME"),
        "password": os.environ.get("CRM_PASSWORD"),
    }
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
    }

    try:
        response = requests.post(url=crm_token_url, data=payload, headers=headers)
        response.raise_for_status()
        print(response.json())
        access_token = response.json()["access_token"]
        instance_url = response.json()["instance_url"]
        logger.info("Login to CRM successful")
    except Exception as e:
        logger.error(f"Error in CRM login: {e}")
        raise

    return access_token, instance_url


def create_error_response(status_code, request_id, error_details):
    return Response(
        status_code=status_code,
        content_type=content_types.APPLICATION_JSON,
        body=json.dumps(
            create_response_body(
                status_code=status_code,
                request_id=request_id,
                error_details=error_details,
            )
        ),
    )


@app.get("/crm/data-access")
@tracer.capture_method
def get_crm():
    request_id = os.environ["_X_AMZN_TRACE_ID"].split("=")[1].split(";")[0]

    request_info = extract_info_from_api_gateway_proxy_event(app.current_event)
    logger.info(f"Request event: {request_info}")
    get_endpoint = os.environ.get("GET_ENDPOINT")
    query_params = app.current_event.query_string_parameters
    query_param_string = ""
    for key, value in query_params.items():
        query_param_string += f"{key}={urllib.parse.quote(value)}&"

    try:
        crm_token, instance_url = get_crm_token_url()
    except Exception as e:
        logger.error(f"Can't login to crm: {e}")
        return create_error_response(
            status_code=HTTPStatus.UNAUTHORIZED,
            request_id=request_id,
            error_details="Can't login to crm",
        )

    def fetch_all_records(url, headers, all_records=None):
        print(url)
        if all_records is None:
            all_records = []

        try:
            response = requests.get(url=url, headers=headers)
            logger.info(f"Request: {url}")
            response.raise_for_status()
            response_data = response.json()
            logger.info(f"Response: {response_data}")
            all_records.extend(response_data.get('records', []))
            
            if not response_data.get('done', True):
                next_records_url = response_data.get('nextRecordsUrl')
                if next_records_url:
                    full_next_url = instance_url + next_records_url
                    return fetch_all_records(full_next_url, headers, all_records)
            
            return {
                'totalSize': len(all_records),
                'done': True,
                'records': all_records
            }

        except requests.RequestException as e:
            if hasattr(e, "response"):
                logger.error(e.response.text)
                try:
                    status_code = HTTPStatus(e.response.status_code)
                    error_message = e.response.json()[0].get("message", str(e))
                except (ValueError, AttributeError, json.JSONDecodeError):
                    status_code = HTTPStatus.BAD_GATEWAY
                    error_message = str(e)
            else:
                status_code = HTTPStatus.BAD_GATEWAY
                error_message = f"Error occurred while fetching crm data: {str(e)}"
            logger.error(f"Request exception: {error_message}")
            raise

    try:
        initial_url = (
            instance_url + get_endpoint + f"?{query_param_string[:-1]}"
            if query_param_string
            else instance_url + get_endpoint
        )
        
        headers = {"Authorization": "Bearer " + crm_token}
        
        full_response = fetch_all_records(initial_url, headers)
        
        logger.info(f"Total records fetched: {full_response['totalSize']}")
        return create_response_body(
            status_code=HTTPStatus.OK,
            request_id=request_id,
            return_payload=full_response,
        )
    
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        return create_error_response(
            HTTPStatus.INTERNAL_SERVER_ERROR, request_id, "An unexpected error occurred"
        )


@app.post("/crm/data-access")
@tracer.capture_method
def create_crm():
    request_id = os.environ["_X_AMZN_TRACE_ID"].split("=")[1].split(";")[0]
    request_info = extract_info_from_api_gateway_proxy_event(app.current_event)
    logger.info(f"Request event: {request_info}")
    body = app.current_event.get("body")
    body = json.loads(body)
    post_endpoint = os.environ.get("POST_ENDPOINT")
    sforce_auto_assign = app.current_event.headers.get("Sforce-Auto-Assign")

    try:
        crm_token, instance_url = get_crm_token_url()
    except Exception as e:
        logger.error(f"Can't login to crm: {e}")
        return create_error_response(
            status_code=HTTPStatus.UNAUTHORIZED,
            request_id=request_id,
            error_details="Can't login to crm",
        )

    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + crm_token,
        **({"Sforce-Auto-Assign": sforce_auto_assign} if sforce_auto_assign else {})
    }
    print(headers)
    try:
        response = requests.post(
            url=instance_url + post_endpoint, data=json.dumps(body), headers=headers
        )
        response.raise_for_status()
        logger.info(f"Response: {response.text}")
        return create_response_body(
            status_code=HTTPStatus.OK,
            request_id=request_id,
            return_payload=response.json(),
        )
    except requests.RequestException as e:
        if hasattr(e, "response"):
            try:
                status_code = HTTPStatus(e.response.status_code)
                error_message = e.response.json().get("message", str(e))
                logger.error(e.response.text)
            except (ValueError, AttributeError, json.JSONDecodeError):
                status_code = HTTPStatus.BAD_GATEWAY
                error_message = str(e)
                logger.error(e.response.text)

        else:
            status_code = HTTPStatus.BAD_GATEWAY
            error_message = f"Error occurred while creating crm data: {str(e)}"
        logger.error(f"Request exception: {error_message}")
        return create_error_response(status_code, request_id, error_message)
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        return create_error_response(
            HTTPStatus.INTERNAL_SERVER_ERROR, request_id, "An unexpected error occurred"
        )


@app.patch("/crm/data-access")
@tracer.capture_method
def update_crm():
    request_id = os.environ["_X_AMZN_TRACE_ID"].split("=")[1].split(";")[0]

    request_info = extract_info_from_api_gateway_proxy_event(app.current_event)
    logger.info(f"Request event: {request_info}")
    patch_endpoint = os.environ.get("POST_ENDPOINT")
    body = app.current_event.body
    body = json.loads(body)
    sforce_auto_assign = app.current_event.headers.get("Sforce-Auto-Assign")

    try:
        crm_token, instance_url = get_crm_token_url()
    except Exception as e:
        logger.error(f"Can't login to crm: {e}")
        return create_error_response(
            status_code=HTTPStatus.UNAUTHORIZED,
            request_id=request_id,
            error_details="Can't login to crm",
        )

    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + crm_token,
        **({"Sforce-Auto-Assign": sforce_auto_assign} if sforce_auto_assign else {})
    }

    try:
        response = requests.patch(
            url=instance_url + patch_endpoint, data=json.dumps(body), headers=headers
        )
        response.raise_for_status()
        logger.info(f"Response: {response.text}")
        return create_response_body(
            status_code=HTTPStatus.OK,
            request_id=request_id,
            return_payload=response.json(),
        )
    except requests.RequestException as e:
        if hasattr(e, "response"):
            try:
                status_code = HTTPStatus(e.response.status_code)
                error_message = e.response.json().get("message", str(e))
            except (ValueError, AttributeError, json.JSONDecodeError):
                status_code = HTTPStatus.BAD_GATEWAY
                error_message = str(e)
        else:
            status_code = HTTPStatus.BAD_GATEWAY
            error_message = f"Error occurred while updating crm data: {str(e)}"
        logger.error(f"Request exception: {error_message}")
        return create_error_response(status_code, request_id, error_message)
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        return create_error_response(
            HTTPStatus.INTERNAL_SERVER_ERROR, request_id, "An unexpected error occurred"
        )


@app.delete("/crm/data-access")
@tracer.capture_method
def delete_crm():
    request_id = os.environ["_X_AMZN_TRACE_ID"].split("=")[1].split(";")[0]

    request_info = extract_info_from_api_gateway_proxy_event(app.current_event)
    logger.info(f"Request event: {request_info}")
    delete_endpoint = os.environ.get("POST_ENDPOINT")
    query_params = app.current_event.query_string_parameters
    query_param_string = ""
    for key, value in query_params.items():
        query_param_string += f"{key}={value}&"

    try:
        crm_token, instance_url = get_crm_token_url()
    except Exception as e:
        logger.error(f"Can't login to crm: {e}")
        return create_error_response(
            status_code=HTTPStatus.UNAUTHORIZED,
            request_id=request_id,
            error_details="Can't login to crm",
        )
    headers = {"Authorization": "Bearer " + crm_token}

    try:
        response = requests.delete(
            url=instance_url + delete_endpoint + f"?{query_param_string[:-1]}"
            if query_param_string
            else "",
            headers=headers,
        )
        response.raise_for_status()
        logger.info(f"Response: {response.text}")
        return create_response_body(
            status_code=HTTPStatus.OK,
            request_id=request_id,
            return_payload=response.json(),
        )
    except requests.RequestException as e:
        if hasattr(e, "response"):
            try:
                status_code = HTTPStatus(e.response.status_code)
                error_message = e.response.json().get("message", str(e))
            except (ValueError, AttributeError, json.JSONDecodeError):
                status_code = HTTPStatus.BAD_GATEWAY
                error_message = str(e)
        else:
            status_code = HTTPStatus.BAD_GATEWAY
            error_message = f"Error occurred while deleting crm data: {str(e)}"
        logger.error(f"Request exception: {error_message}")
        return create_error_response(status_code, request_id, error_message)
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        return create_error_response(
            HTTPStatus.INTERNAL_SERVER_ERROR, request_id, "An unexpected error occurred"
        )


@logger.inject_lambda_context
@tracer.capture_lambda_handler
def lambda_handler(event: dict, context: LambdaContext):
    logger.info(event)
    return app.resolve(event, context)
